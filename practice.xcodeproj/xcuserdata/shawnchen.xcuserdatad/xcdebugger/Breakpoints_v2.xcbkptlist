<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "5265D679-605D-411C-A542-E21EEC6E7817"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0E0B5320-6E88-4894-93DF-6C82F6F2EC49"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/Recording/Waveform.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "27"
            endingLineNumber = "27"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D3E7F324-**************-F8D75D4C8D16"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/Recording/Waveform.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "19"
            endingLineNumber = "19"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "D108B0AB-7784-4362-98C6-61C1C0C7BE52"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/Model/TaskItem.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "130"
            endingLineNumber = "130"
            landmarkName = "TaskItem"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E4494E6B-44AF-4B89-A66E-94143BADE788"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/CountDown/CountupTimerLiveActivity.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "5"
            endingLineNumber = "5"
            landmarkName = "CountingUpTimerAttributes"
            landmarkType = "14">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "14B96EFD-5E88-437D-8B50-290A6BAD0768"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/AchievementList/AchievementListView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "102"
            endingLineNumber = "102"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "20646250-6A36-482E-ABB5-9635510D5F55"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/AchievementList/SharePreviewFullScreenView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "74"
            endingLineNumber = "74"
            landmarkName = "saveImage()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "058D940B-366B-48DD-92D1-F8532D016390"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/practiceApp.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "31"
            endingLineNumber = "31"
            landmarkName = "AppDelegate"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0CCEF050-8346-45D3-A54D-37B3374716AE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/practiceApp.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "40"
            endingLineNumber = "40"
            landmarkName = "application(_:didFinishLaunchingWithOptions:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F3909DD4-76C8-4E05-91AE-3E0D40EB4F0D"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/ImageManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "149"
            endingLineNumber = "149"
            landmarkName = "preloadImages(paths:maxConcurrent:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "614CDCF4-EDCA-4BD9-9402-2A48B9D1A2C3"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/ImageManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "160"
            endingLineNumber = "160"
            landmarkName = "ImageManager"
            landmarkType = "3">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F8AF7AD2-68EE-4336-8095-1D16E891F7A0"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/ImageManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "128"
            endingLineNumber = "128"
            landmarkName = "loadImage(path:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3DC1CAFE-BA7E-4D52-96E9-823FB33A2A1C"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/ImageManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "144"
            endingLineNumber = "144"
            landmarkName = "preloadImages(paths:maxConcurrent:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "76A9764D-D74F-4CEA-B26D-6710E2BDC0E2"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/BasicComponent/ArcProgressView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "21"
            endingLineNumber = "21"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5359138A-4CA6-411C-8AAB-70E82463B20E"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/Subscribe/CustomSubscribeView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "142"
            endingLineNumber = "142"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "16897828-F0E0-48AF-8556-9A3E68C6C299"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/Subscribe/CustomSubscribeView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "148"
            endingLineNumber = "148"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "9A8F823C-1C5F-4F8F-A35F-28C393CE30F6"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/MembershipManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "108"
            endingLineNumber = "108"
            landmarkName = "freeTrialDescription(for:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "AD7EE1FC-E9C0-45B5-B422-03C3EFB6156A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/PracticeView/StreakCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "32"
            endingLineNumber = "32"
            landmarkName = "loadStreakData()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "81B77802-E1B5-4365-9506-17F78B95F674"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/PracticeView/StreakCalendarView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "201"
            endingLineNumber = "201"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "848EDC48-2F00-4909-B560-E834D878B33D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/RecordingManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "161"
            endingLineNumber = "161"
            landmarkName = "saveRecording(task:url:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "3BFDF997-95F0-447E-AA5B-E1648B4BC5F1"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/RecordingManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "204"
            endingLineNumber = "204"
            landmarkName = "importRecording(from:to:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "366492D9-31AF-449B-A653-70FD87947C33"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/RecordingManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "179"
            endingLineNumber = "179"
            landmarkName = "importRecording(from:to:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "78505F0C-87CB-42D9-AB18-06685CEB0520"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/VideoManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "44"
            endingLineNumber = "44"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "7EFF0B23-4FD6-4374-9C99-51DBB2829395"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/TaskDetail/VideoItemView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "104"
            endingLineNumber = "104"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E21A6523-9FB3-4552-81D5-ACF096BFA9B0"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/TaskDetail/VideoItemView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "149"
            endingLineNumber = "149">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CA33CD38-DA3F-42D3-A8EB-FB3EF64AB3FD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/TaskDetail/VideoItemView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "95"
            endingLineNumber = "95"
            landmarkName = "unknown"
            landmarkType = "0">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0BC7C868-7271-4FB7-B4F8-6A6D61DD4FD2"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/NotificationManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "361"
            endingLineNumber = "361"
            landmarkName = "playCountdownFinishedSound(at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "92706B28-B4A1-49DB-802C-0AD80F047ABA"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/NotificationManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "339"
            endingLineNumber = "339"
            landmarkName = "playCountdownFinishedSound(at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C9F418F0-A030-47DE-B26A-F058485957F5"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/CountDown/CountDownView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "253"
            endingLineNumber = "253"
            landmarkName = "scheduleNotification()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "25C8C241-5B49-4129-A303-3D6BD35F4D29"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/NotificationManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "347"
            endingLineNumber = "347"
            landmarkName = "playCountdownFinishedSound(at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B1689309-A3DA-49AF-8082-BD99A52E4AFE"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/TaskListManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "79"
            endingLineNumber = "79"
            landmarkName = "loadData()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "DE09094F-D71F-45D2-8DC4-52F6A38DD669"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/TaskListManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "92"
            endingLineNumber = "92"
            landmarkName = "TaskListManager"
            landmarkType = "3">
            <Locations>
               <Location
                  uuid = "DE09094F-D71F-45D2-8DC4-52F6A38DD669 - 3bae74cb2eeda7c0"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "practice.TaskListManager.loadDataAsync() async -&gt; ()"
                  moduleName = "practice.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/code/Practice-swift/practice/ViewModels/TaskListManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "93"
                  endingLineNumber = "93">
               </Location>
               <Location
                  uuid = "DE09094F-D71F-45D2-8DC4-52F6A38DD669 - 158ba59dc136f257"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "(1) suspend resume partial function for practice.TaskListManager.loadDataAsync() async -&gt; ()"
                  moduleName = "practice.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/code/Practice-swift/practice/ViewModels/TaskListManager.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "93"
                  endingLineNumber = "93">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "F95CAC0A-DF98-44D8-B206-D80B7E0DD2AC"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/TaskDetail/TaskDetailView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "59"
            endingLineNumber = "59"
            landmarkName = "TaskDetailView"
            landmarkType = "14">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C6130E53-76DD-447A-9BBC-9DC28503580E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/TaskDetail/TaskDetailView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "179"
            endingLineNumber = "179"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "91EF4443-A3C0-4A71-8D8F-9FC213B70623"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/View/TaskDetail/TaskDetailView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "174"
            endingLineNumber = "174"
            landmarkName = "body"
            landmarkType = "24">
            <Locations>
               <Location
                  uuid = "91EF4443-A3C0-4A71-8D8F-9FC213B70623 - 2927121bfc8f6c47"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "(1) suspend resume partial function for closure #1 () async -&gt; () in closure #5 (Swift.Result&lt;Swift.Array&lt;Foundation.URL&gt;, Swift.Error&gt;) -&gt; () in practice.TaskDetailView.body.getter : some"
                  moduleName = "practice.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/code/Practice-swift/practice/View/TaskDetail/TaskDetailView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "176"
                  endingLineNumber = "176">
               </Location>
               <Location
                  uuid = "91EF4443-A3C0-4A71-8D8F-9FC213B70623 - f02b5e8c3e4ecb61"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "(2) await resume partial function for closure #1 () async -&gt; () in closure #5 (Swift.Result&lt;Swift.Array&lt;Foundation.URL&gt;, Swift.Error&gt;) -&gt; () in practice.TaskDetailView.body.getter : some"
                  moduleName = "practice.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/code/Practice-swift/practice/View/TaskDetail/TaskDetailView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "176"
                  endingLineNumber = "176">
               </Location>
               <Location
                  uuid = "91EF4443-A3C0-4A71-8D8F-9FC213B70623 - 8f1afa3f7968a859"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "(3) suspend resume partial function for closure #1 () async -&gt; () in closure #5 (Swift.Result&lt;Swift.Array&lt;Foundation.URL&gt;, Swift.Error&gt;) -&gt; () in practice.TaskDetailView.body.getter : some"
                  moduleName = "practice.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/code/Practice-swift/practice/View/TaskDetail/TaskDetailView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "176"
                  endingLineNumber = "176">
               </Location>
               <Location
                  uuid = "91EF4443-A3C0-4A71-8D8F-9FC213B70623 - bc04363134450a5a"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "(4) suspend resume partial function for closure #1 () async -&gt; () in closure #5 (Swift.Result&lt;Swift.Array&lt;Foundation.URL&gt;, Swift.Error&gt;) -&gt; () in practice.TaskDetailView.body.getter : some"
                  moduleName = "practice.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Documents/code/Practice-swift/practice/View/TaskDetail/TaskDetailView.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "176"
                  endingLineNumber = "176">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "68CE3E28-3D8F-4D1A-9C53-55932BD5950D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "practice/ViewModels/RecordingManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "223"
            endingLineNumber = "223"
            landmarkName = "extractAudioFromVideo(videoURL:to:at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
